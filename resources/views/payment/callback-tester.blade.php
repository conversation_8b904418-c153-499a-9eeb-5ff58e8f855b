@extends('layouts.contentNavbarLayout')

@section('title', 'Tripay Callback Tester')

@section('content')
    <div class="container-xxl flex-grow-1 container-p-y">
        <h4 class="fw-bold py-3 mb-4">
            <span class="text-muted fw-light">Payment /</span> Tripay Callback Tester
        </h4>

        <div class="row">
            <div class="col-md-12">
                <div class="card mb-4">
                    <h5 class="card-header">Test Tripay Payment Callback</h5>
                    <div class="card-body">
                        @if (session('success'))
                            <div class="alert alert-success">
                                {{ session('success') }}
                            </div>
                        @endif

                        @if (session('error'))
                            <div class="alert alert-danger">
                                {{ session('error') }}
                            </div>
                        @endif

                        <div class="alert alert-info">
                            <h6>Callback URL Information</h6>
                            <p>Your callback URLs are:</p>
                            <ul>
                                <li>Standard: <code>{{ $callbackUrl }}</code></li>
                                <li>API: <code>{{ url('/api/payment/callback') }}</code></li>
                                <li>Fallback: <code>{{ url('/tripay-callback') }}</code></li>
                            </ul>
                            <p>This tool allows you to simulate a payment callback from Tripay to mark an invoice as paid
                                without actually making a payment.</p>
                        </div>

                        <ul class="nav nav-tabs mb-3" id="callbackTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="standard-tab" data-bs-toggle="tab"
                                    data-bs-target="#standard" type="button" role="tab" aria-controls="standard"
                                    aria-selected="true">Standard Callback</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="fallback-tab" data-bs-toggle="tab" data-bs-target="#fallback"
                                    type="button" role="tab" aria-controls="fallback" aria-selected="false">Fallback
                                    Callback</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="direct-tab" data-bs-toggle="tab" data-bs-target="#direct"
                                    type="button" role="tab" aria-controls="direct" aria-selected="false">Direct
                                    Test</button>
                            </li>
                        </ul>

                        <div class="tab-content" id="callbackTabsContent">
                            <div class="tab-pane fade show active" id="standard" role="tabpanel"
                                aria-labelledby="standard-tab">
                                <form action="{{ route('payment.callback.test') }}" method="POST">
                                    @csrf
                                    <div class="mb-3">
                                        <label for="invoice_id" class="form-label">Select Invoice to Mark as Paid</label>
                                        <select class="form-select" id="invoice_id" name="invoice_id" required>
                                            <option value="">-- Select an Invoice --</option>
                                            @foreach ($invoices as $invoice)
                                                <option value="{{ $invoice->id }}">
                                                    Invoice #{{ $invoice->id }} - {{ $invoice->customer->nama_customer }} -
                                                    Rp
                                                    {{ number_format($invoice->tagihan, 0, ',', '.') }}
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <button type="submit" class="btn btn-primary">Simulate Standard Callback</button>
                                </form>
                            </div>

                            <div class="tab-pane fade" id="fallback" role="tabpanel" aria-labelledby="fallback-tab">
                                <div class="mb-3">
                                    <label for="fallback_invoice_id" class="form-label">Select Invoice to Mark as
                                        Paid</label>
                                    <select class="form-select" id="fallback_invoice_id">
                                        <option value="">-- Select an Invoice --</option>
                                        @foreach ($invoices as $invoice)
                                            <option value="{{ $invoice->id }}">
                                                Invoice #{{ $invoice->id }} - {{ $invoice->customer->nama_customer }} -
                                                Rp
                                                {{ number_format($invoice->tagihan, 0, ',', '.') }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                <button type="button" id="testFallbackBtn" class="btn btn-primary">Test Fallback
                                    Callback</button>
                            </div>

                            <div class="tab-pane fade" id="direct" role="tabpanel" aria-labelledby="direct-tab">
                                <div class="mb-3">
                                    <label for="direct_invoice_id" class="form-label">Select Invoice to Mark as Paid</label>
                                    <select class="form-select" id="direct_invoice_id">
                                        <option value="">-- Select an Invoice --</option>
                                        @foreach ($invoices as $invoice)
                                            <option value="{{ $invoice->id }}">
                                                Invoice #{{ $invoice->id }} - {{ $invoice->customer->nama_customer }} -
                                                Rp
                                                {{ number_format($invoice->tagihan, 0, ',', '.') }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                <button type="button" id="testDirectBtn" class="btn btn-primary">Test Direct
                                    Callback</button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <h5 class="card-header">How It Works</h5>
                    <div class="card-body">
                        <p>This tool simulates a payment callback from Tripay by:</p>
                        <ol>
                            <li>Creating a test payload similar to what Tripay would send</li>
                            <li>Sending it directly to your callback handler</li>
                            <li>Updating the invoice status to paid (status_id = 8)</li>
                        </ol>
                        <p>This is useful for testing your payment flow without having to make actual payments through
                            Tripay.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('page-script')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Test Fallback Callback
            document.getElementById('testFallbackBtn').addEventListener('click', function() {
                const invoiceId = document.getElementById('fallback_invoice_id').value;
                if (!invoiceId) {
                    alert('Please select an invoice');
                    return;
                }

                // Open the fallback URL in a new tab
                const fallbackUrl = '{{ url('/tripay-callback') }}?test_mode=1&invoice_id=' + invoiceId;
                window.open(fallbackUrl, '_blank');
            });

            // Test Direct Callback
            document.getElementById('testDirectBtn').addEventListener('click', function() {
                const invoiceId = document.getElementById('direct_invoice_id').value;
                if (!invoiceId) {
                    alert('Please select an invoice');
                    return;
                }

                // Open the direct test URL in a new tab
                const directUrl = '{{ url('/payment/test') }}/' + invoiceId;
                window.open(directUrl, '_blank');
            });
        });
    </script>
@endsection
